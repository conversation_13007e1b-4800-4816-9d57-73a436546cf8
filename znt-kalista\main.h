/*
 * main.h - Core definitions and data for Kalista bot
 *
 * This file contains the main data structures, constants, and core functions
 * for the Kalista bot implementation. It defines spell instances, ability
 * constants, prediction data, damage calculations, and other essential
 * components that are used throughout the bot.
 *
 * <AUTHOR>
 * @date May 26, 2025
 */

#pragma once
#include "config.h"
#include "sdk.h"

namespace kalista {
    // ========================================
    // TIMING AND RATE LIMITING
    // ========================================

    // Current game time - updated each frame for timing calculations
    inline static f32 time = 0.0f;

    // Next allowed cast time - prevents spam casting and respects ability cooldowns
    inline static f32 next_cast = 0.0f;

    // ========================================
    // PLAYER AND SPELL INSTANCES
    // ========================================

    // Reference to our player character - set during initialization
    inline static AIHeroClient* player;

    // Kalista's spell instances - used for checking cooldowns, levels, etc.
    inline static SpellInstance* kalista_q;  // Pierce (Q)
    inline static SpellInstance* kalista_e;  // Rend (E)
    inline static SpellInstance* kalista_r;  // Fate's Call (R)

    // ========================================
    // ABILITY CONSTANTS
    // ========================================

    // General casting rate limit - minimum time between spell casts (in seconds)
    constexpr f32 CAST_RATE = 0.25f;

    // E (Rend) ability constants
    constexpr f32 E_RANGE = 1100.0f;  // Maximum range for Rend activation

    // Q (Pierce) ability constants
    constexpr f32 Q_RANGE = 1200.0f;  // Maximum cast range
    constexpr f32 Q_WIDTH = 80.0f;    // Projectile width (radius)
    constexpr f32 Q_SPEED = 2400.0f;  // Projectile travel speed (units/second)
    constexpr f32 Q_DELAY = 0.25f;    // Cast time delay before projectile fires

    // ========================================
    // VISUAL THEME COLORS
    // ========================================

    // Kalista's signature light purple color scheme for visual indicators
    constexpr u8 THEME_RED   = 147;  // Light purple red component
    constexpr u8 THEME_GREEN = 112;  // Light purple green component
    constexpr u8 THEME_BLUE  = 219;  // Light purple blue component
    constexpr u8 THEME_ALPHA = 200;  // Semi-transparent for overlays

    // ========================================
    // SPELL PREDICTION CONFIGURATION
    // ========================================

    // Prediction settings for Q (Pierce) - used by the prediction system to calculate
    // where to aim the spell based on target movement, cast delay, projectile speed, etc.
    inline static PredSpell pred_q = {
        .spell_type                  = PredSpellType::Linear,  // Straight line skillshot
        .cast_delay                  = Q_DELAY,                // Time before projectile fires
        .radius                      = Q_WIDTH,                // Projectile width
        .range                       = Q_RANGE,                // Maximum cast distance
        .speed                       = Q_SPEED,                // How fast projectile travels
        .hitbox_with_bounding_radius = true,                   // Account for target hitboxes
        .collisions                  = PredCollisionType::Minion | PredCollisionType::Hero | PredCollisionType::Windwall,
    };

    // ========================================
    // DAMAGE CALCULATION FUNCTIONS
    // ========================================

    // Calculates the total damage that Rend (E) would deal to a target
    // Takes into account base damage, AD/AP scaling, spear stacks, and damage reduction
    inline static f32 calc_e_damage(AIBaseClient* target) {
        f32 damage = 0.0f;
        if (!target) {
            return damage;
        }

        i32 spell_level = kalista_e->level();
        if (spell_level == 0) {
            return damage;
        }

        // Rend base damage values per level (20/30/40/50/60 + 60% AD + 20% AP)
        f32 raw_rend_damage[]            = {20.0f, 30.0f, 40.0f, 50.0f, 60.0f};
        f32 raw_rend_damage_multiplier[] = {0.60f, 0.60f, 0.60f, 0.60f, 0.60f};

        // Additional damage per spear stack beyond the first
        f32 raw_rend_damage_per_stack[]            = {10.0f, 14.0f, 19.0f, 25.0f, 32.0f};
        f32 raw_rend_damage_per_stack_multiplier[] = {0.20f, 0.25f, 0.30f, 0.35f, 0.40f};

        // Get our current AD and AP for scaling calculations
        f32 player_ad = player->physical_damage();
        f32 player_ap = player->magical_damage();

        // Calculate base damage from the first spear (20/30/40/50/60 + 60% AD + 20% AP)
        damage = raw_rend_damage[spell_level - 1] + (player_ad * raw_rend_damage_multiplier[spell_level - 1]) + (player_ap * 0.20f);

        // Check if target has spear stacks (the Rend marker buff)
        auto e_buff = target->get_buff(SpellHash("KalistaExpungeMarker"));
        if (!e_buff) {
            return 0.0f;  // No spears = no damage
        }

        i32 stacks = e_buff->count();

        // Add damage from additional spears beyond the first one
        // Each additional spear does (10/14/19/25/32 + 20/25/30/35/40% AD + 20% AP)
        if (stacks > 1) {
            i32 additional_spears = stacks - 1;
            damage += additional_spears * (raw_rend_damage_per_stack[spell_level - 1] +
                                              (player_ad * raw_rend_damage_per_stack_multiplier[spell_level - 1]) + (player_ap * 0.20f));
        }

        // Special case: Rend deals 50% damage against epic monsters (Baron, Dragon, etc.)
        if (target->is_minion()) {
            auto minion = target->as_minion();
            if (minion->is_epic()) {
                damage *= 0.5f;
            }
        }

        // Apply armor/magic resist and return final damage
        return player->calc_physical_damage(target, damage);
    }

};  // namespace kalista